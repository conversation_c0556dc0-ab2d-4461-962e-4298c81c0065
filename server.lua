local ESX = exports['es_extended']:getSharedObject()

-- الدالة للتحقق من الهوية
local function checkIdentity(playerId, identifier)
    if not playerId or not identifier then
        print('[IDENTITY] Invalid player data - ID:', playerId, 'Identifier:', identifier)
        return
    end

    MySQL.query('SELECT firstname, lastname FROM users WHERE identifier = ?', {
        identifier
    }, function(result)
        if result and result[1] then
            if not result[1].firstname or result[1].firstname == '' then
                TriggerClientEvent('OscarCounty_Identity:forceCreateIdentity', playerId)
            end
        else
            TriggerClientEvent('OscarCounty_Identity:forceCreateIdentity', playerId)
        end
    end)
end

-- Listen for player loaded event (الحدث الصحيح لـ ESX)
AddEventHandler('esx:playerLoaded', function(playerId, xPlayer)
    if playerId and xPlayer and xPlayer.identifier then
        -- انتظار إضافي للتأكد من تحميل البيانات بالكامل
        Citizen.SetTimeout(3000, function()
            checkIdentity(playerId, xPlayer.identifier)
        end)
    end
end)

-- حدث بديل عند انضمام اللاعب للخادم
AddEventHandler('playerConnecting', function(name, setKickReason, deferrals)
    local src = source
    if not src then return end

    -- انتظار تحميل ESX للاعب
    Citizen.SetTimeout(8000, function()
        local xPlayer = ESX.GetPlayerFromId(src)
        if xPlayer and xPlayer.identifier then
            checkIdentity(src, xPlayer.identifier)
        end
    end)
end)

-- عند انتهاء اختيار الشخصية (من z_identity_hook)
RegisterNetEvent('OscarCounty_Identity:onCharacterInitialized', function()
    local src = source
    if not src then return end
    local identifier = ESX.GetIdentifier(src)

    if not identifier then return end

    print(('[IDENTITY] Checking identity for %s'):format(identifier))

    MySQL.query('SELECT firstname, lastname, dateofbirth, sex FROM users WHERE identifier = ?', {
        identifier
    }, function(result)
        if result and result[1] then
            local identity = result[1]
            if not identity.firstname or identity.firstname == '' or identity.firstname == 'غير معروف' then
                print(('[IDENTITY] Missing identity for %s - triggering identity UI'):format(identifier))
                TriggerClientEvent('OscarCounty_Identity:forceCreateIdentity', src)
            else
                print(('[IDENTITY] Identity exists for %s: %s %s'):format(identifier, identity.firstname, identity.lastname))
            end
        else
            print(('[IDENTITY] No record found for %s - triggering identity UI'):format(identifier))
            TriggerClientEvent('OscarCounty_Identity:forceCreateIdentity', src)
        end
    end)
end)

-- حدث للتحقق من الهوية يدوياً
RegisterNetEvent('OscarCounty_Identity:checkIdentity', function()
    local src = source
    if not src then return end
    local xPlayer = ESX.GetPlayerFromId(src)
    if not xPlayer then
        print('[IDENTITY] xPlayer not found for source:', src)
        return
    end

    print('[IDENTITY] Manual identity check triggered for:', xPlayer.identifier)
    checkIdentity(src, xPlayer.identifier)
end)

-- حدث إضافي للتحقق من الهوية عند الحاجة
AddEventHandler('onResourceStart', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        print('[IDENTITY] Resource started successfully')
        -- التحقق من جميع اللاعبين المتصلين حالياً
        Citizen.SetTimeout(2000, function()
            local players = ESX.GetPlayers()
            for _, playerId in ipairs(players) do
                local xPlayer = ESX.GetPlayerFromId(playerId)
                if xPlayer and xPlayer.identifier then
                    checkIdentity(playerId, xPlayer.identifier)
                end
            end
        end)
    end
end)

-- عند إنشاء هوية جديدة من العميل
RegisterNetEvent('OscarCounty_Identity:createNewIdentity', function(data)
    local src = source
    if not src then
        print('[IDENTITY] Error: Invalid source')
        return
    end
    local xPlayer = ESX.GetPlayerFromId(src)
    if not xPlayer then
        print(('[IDENTITY] Error: xPlayer not found for source %s'):format(src))
        return
    end

    local firstName = data.firstName or "غير معروف"
    local lastName  = data.lastName or "غير معروف"
    local dob       = data.dob or "2000-01-01"
    local sex       = data.sex or "m"

    MySQL.update('UPDATE users SET firstname = ?, lastname = ?, dateofbirth = ?, sex = ? WHERE identifier = ?', {
        firstName, lastName, dob, sex, xPlayer.identifier
    }, function(rowsChanged)
        if rowsChanged > 0 then
            print(('[IDENTITY] Created identity for %s | %s %s | DOB: %s | Sex: %s'):format(
                xPlayer.identifier, firstName, lastName, dob, sex
            ))
            -- إشعار العميل بنجاح إنشاء الهوية
            TriggerClientEvent('OscarCounty_Identity:identityCreated', src)
        else
            print(('[IDENTITY] Failed to create identity for %s'):format(xPlayer.identifier))
        end
    end)
end)

-- عند حفظ الهوية من واجهة NUI (للتوافق مع الإصدارات السابقة)
RegisterNetEvent('OscarCounty_Identity:saveIdentity', function(data)
    local src = source
    if not src then
        print('[IDENTITY] Error: Invalid source')
        return
    end
    local xPlayer = ESX.GetPlayerFromId(src)
    if not xPlayer then
        print(('[IDENTITY] Error: xPlayer not found for source %s'):format(src))
        return
    end

    local firstName = data.firstName or "غير معروف"
    local lastName  = data.lastName or "غير معروف"
    local dob       = data.dob or "2000-01-01"
    local sex       = data.sex or "m"

    MySQL.update('UPDATE users SET firstname = ?, lastname = ?, dateofbirth = ?, sex = ? WHERE identifier = ?', {
        firstName, lastName, dob, sex, xPlayer.identifier
    }, function(rowsChanged)
        if rowsChanged > 0 then
            print(('[IDENTITY] Saved identity for %s | %s %s | DOB: %s | Sex: %s'):format(
                xPlayer.identifier, firstName, lastName, dob, sex
            ))
        else
            print(('[IDENTITY] Failed to update identity for %s'):format(xPlayer.identifier))
        end
    end)
end)
