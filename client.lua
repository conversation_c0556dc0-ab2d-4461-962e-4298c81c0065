local ESX = exports["es_extended"]:getSharedObject()
local open = false
local isNewPlayer = false

local markerLocation = vector3(611.3255, 9.4899, 75.0449)
local nearMarker = false

Citizen.CreateThread(function()
    local blip = AddBlipForCoord(markerLocation.x, markerLocation.y, markerLocation.z)
    SetBlipSprite(blip, 58)
    SetBlipDisplay(blip, 4)
    SetBlipScale(blip, 0.9)
    SetBlipColour(blip, 47)
    SetBlipAsShortRange(blip, true)
    
    BeginTextCommandSetBlipName("STRING")
	AddTextComponentString("<FONT FACE='A9eelsh'>ﺔﻳﻮﻬﻟﺍ ﺮﻴﻴﻐﺗ")
    EndTextCommandSetBlipName(blip)
end)

RegisterNetEvent('OscarCounty_Identity:forceCreateIdentity')
AddEventHandler('OscarCounty_Identity:forceCreateIdentity', function()
    if open then return end
    
    open = true
    isNewPlayer = true
    ESX.UI.Menu.CloseAll()
    
    SendNUIMessage({
        type = 'createId',
        show = true,
        isNewPlayer = true,
        isPaid = false
    })
    
    SetNuiFocus(true, true)
end)

RegisterNetEvent('OscarCounty_Identity:openClothingMenu')
AddEventHandler('OscarCounty_Identity:openClothingMenu', function()
    ESX.UI.Menu.CloseAll()
    open = false
    SetNuiFocus(false, false)
    
    Citizen.SetTimeout(500, function()
        TriggerEvent('skinchanger:openMenu')
        TriggerEvent('esx_skin:openSaveableMenu')
    end)
end)

local function getMugshotForPlayer(sourcePlayerId, userData, messageType)
    local targetPlayer = nil
    local players = GetActivePlayers()
    
    for _, playerId in ipairs(players) do
        if GetPlayerServerId(playerId) == sourcePlayerId then
            targetPlayer = playerId
            break
        end
    end
    
    if targetPlayer then
        local targetPed = GetPlayerPed(targetPlayer)
        local mugshotResult = exports["loaf_headshot_base64"]:getBase64(targetPed)
        
        if mugshotResult and mugshotResult.base64 then
            userData.avatar = mugshotResult.base64
        end
    end
    
    SendNUIMessage({
        type = messageType,
        show = true,
        data = userData
    })
end

RegisterNetEvent('OscarCounty_Identity:showIdentityCard')
AddEventHandler('OscarCounty_Identity:showIdentityCard', function(userData)
    if open then return end
    
    open = true
    ESX.UI.Menu.CloseAll()
    
    local sourcePlayerId = tonumber(userData.id)
    getMugshotForPlayer(sourcePlayerId, userData, 'identityCard')
    
    SetNuiFocus(false, false)
end)

RegisterNetEvent('OscarCounty_Identity:showLicense')
AddEventHandler('OscarCounty_Identity:showLicense', function(licenseData)
    if open then return end
    
    open = true
    ESX.UI.Menu.CloseAll()
    
    local sourcePlayerId = tonumber(licenseData.id)
    getMugshotForPlayer(sourcePlayerId, licenseData, 'drivingLicense')
    
    SetNuiFocus(false, false)
end)

RegisterNetEvent('OscarCounty_Identity:showWeaponLicense')
AddEventHandler('OscarCounty_Identity:showWeaponLicense', function(weaponData)
    if open then return end
    
    open = true
    ESX.UI.Menu.CloseAll()
    
    local sourcePlayerId = tonumber(weaponData.id)
    getMugshotForPlayer(sourcePlayerId, weaponData, 'weaponLicense')
    
    SetNuiFocus(false, false)
end)

RegisterNUICallback('closeUI', function(data, cb)
    if isNewPlayer and data.isNewPlayer then
        cb('blocked')
        return
    end
    
    open = false
    isNewPlayer = false
    SetNuiFocus(false, false)
    
    Citizen.SetTimeout(100, function()
        local playerPed = GetPlayerPed(-1)
        local playerCoords = GetEntityCoords(playerPed)
        local distance = GetDistanceBetweenCoords(playerCoords, markerLocation, true)
        
        if distance <= 1.8 then
            nearMarker = true
            SendNUIMessage({
                type = 'showEntrance',
                show = true
            })
        end
    end)
    
    cb('ok')
end)

RegisterNUICallback('createIdentity', function(data, cb)
    TriggerServerEvent('OscarCounty_Identity:createNewIdentity', data)
    
    local shouldOpenClothing = isNewPlayer or (not data.isPaid)
    
    isNewPlayer = false
    open = false
    SetNuiFocus(false, false)
    
    if shouldOpenClothing then
        Citizen.SetTimeout(1000, function()
            TriggerEvent('OscarCounty_Identity:openClothingMenu')
        end)
    else
        Citizen.SetTimeout(100, function()
            local playerPed = GetPlayerPed(-1)
            local playerCoords = GetEntityCoords(playerPed)
            local distance = GetDistanceBetweenCoords(playerCoords, markerLocation, true)
            
            if distance <= 1.8 then
                nearMarker = true
                SendNUIMessage({
                    type = 'showEntrance',
                    show = true
                })
            end
        end)
    end
    
    cb('ok')
end)

Citizen.CreateThread(function()
    while true do
        local wait = 1000
        local playerPed = GetPlayerPed(-1)
        local playerCoords = GetEntityCoords(playerPed)
        local distance = GetDistanceBetweenCoords(playerCoords, markerLocation, true)
        
        if distance <= 10.0 then
            wait = 0
            DrawMarker(1, markerLocation.x, markerLocation.y, markerLocation.z - 1, 0.0, 0.0, 0.0, 0.0, 0, 0.0, 1.25, 1.25, 0.25, 227, 161, 28, 200, false, false, 2, false, nil, nil, false)
            
            if distance <= 1.8 then
                if not nearMarker and not open then
                    nearMarker = true
                    SendNUIMessage({
                        type = 'showEntrance',
                        show = true
                    })
                end
            else
                if nearMarker then
                    nearMarker = false
                    SendNUIMessage({
                        type = 'showEntrance',
                        show = false
                    })
                end
            end
        else
            if nearMarker then
                nearMarker = false
                SendNUIMessage({
                    type = 'showEntrance',
                    show = false
                })
            end
        end
        
        Wait(wait)
    end
end)

Citizen.CreateThread(function()
    while true do
        Wait(0)
        
        if nearMarker and IsControlJustReleased(0, 38) and not open then
            SendNUIMessage({
                type = 'showEntrance',
                show = false
            })
            
            open = true
            ESX.UI.Menu.CloseAll()
            
            SendNUIMessage({
                type = 'createId',
                show = true,
                isNewPlayer = false,
                isPaid = false
            })
            
            SetNuiFocus(true, true)
        end
        
        if open and (IsControlJustReleased(0, 322) or IsControlJustReleased(0, 177)) then
            if not isNewPlayer then
                SendNUIMessage({
                    type = 'close',
                    show = false
                })
                open = false
                SetNuiFocus(false, false)
                
                Citizen.SetTimeout(100, function()
                    local playerPed = GetPlayerPed(-1)
                    local playerCoords = GetEntityCoords(playerPed)
                    local distance = GetDistanceBetweenCoords(playerCoords, markerLocation, true)
                    
                    if distance <= 1.8 then
                        nearMarker = true
                        SendNUIMessage({
                            type = 'showEntrance',
                            show = true
                        })
                    end
                end)
            end
        end
    end
end)

RegisterNetEvent('esx_skin:playerRegistered')
AddEventHandler('esx_skin:playerRegistered', function()
    Citizen.SetTimeout(1000, function()
        local playerPed = GetPlayerPed(-1)
        local playerCoords = GetEntityCoords(playerPed)
        local distance = GetDistanceBetweenCoords(playerCoords, markerLocation, true)
        
        if distance <= 1.8 then
            nearMarker = true
            SendNUIMessage({
                type = 'showEntrance',
                show = true
            })
        end
    end)
end)

RegisterNetEvent('skinchanger:modelLoaded')
AddEventHandler('skinchanger:modelLoaded', function()
    Citizen.SetTimeout(1000, function()
        local playerPed = GetPlayerPed(-1)
        local playerCoords = GetEntityCoords(playerPed)
        local distance = GetDistanceBetweenCoords(playerCoords, markerLocation, true)
        
        if distance <= 1.8 then
            nearMarker = true
            SendNUIMessage({
                type = 'showEntrance',
                show = true
            })
        end
    end)
end)

RegisterNetEvent('OscarCounty_Identity:clothingMenuClosed')
AddEventHandler('OscarCounty_Identity:clothingMenuClosed', function()
    Citizen.SetTimeout(500, function()
        local playerPed = GetPlayerPed(-1)
        local playerCoords = GetEntityCoords(playerPed)
        local distance = GetDistanceBetweenCoords(playerCoords, markerLocation, true)
        
        if distance <= 1.8 then
            nearMarker = true
            SendNUIMessage({
                type = 'showEntrance',
                show = true
            })
        end
    end)
end)

Citizen.CreateThread(function()
    while true do
        Wait(1000)
        
        if not ESX.UI.Menu.IsOpen('default', GetCurrentResourceName(), 'main') then
            local playerPed = GetPlayerPed(-1)
            local playerCoords = GetEntityCoords(playerPed)
            local distance = GetDistanceBetweenCoords(playerCoords, markerLocation, true)
            
            if distance <= 1.8 and not open then
                if not nearMarker then
                    nearMarker = true
                    SendNUIMessage({
                        type = 'showEntrance',
                        show = true
                    })
                end
            end
        end
    end
end)

RegisterNetEvent('OscarCounty_Identity:identityCreated')
AddEventHandler('OscarCounty_Identity:identityCreated', function()
    Citizen.SetTimeout(5000, function()
        TriggerServerEvent('OscarCounty:GiveStarterPackAfterIdentity')
    end)
end)
